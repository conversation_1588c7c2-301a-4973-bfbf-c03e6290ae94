<script setup>
import PlatformSource from "~/base/views/components/platformSource.vue";
import ShopSelect from "~/base/views/components/shopSelect.vue";
import AddressSelect from "~/base/views/components/addressSelect.vue";
import {useMessage} from "@/hooks/useMessage.js";
import SelectGoods from "~/base/views/components/selectGoods.vue";
import {createFreeOrder, getOrderByTid} from "~/base/api/order.js";

const form = ref({
  fullAddress: [],
  items: [],
  free_order_source: 0,
  is_platform_order: 0,
  order_status: 30,
})
const formdataReveiver = ref({})
const searchParam = ref({})
const receiverType = ref(1)
const goodsType = ref(1)

const {
  getMenuCollapseState
} = useSettingStore()

function addGoods(){
  form.value.items.push({})
}

const showFooter = ref(true)
function initFooter(show, item) {
  showFooter.value = show
  form.value = {
    fullAddress: [],
    items: [],
    free_order_source: 0,
    is_platform_order: 0,
    order_status: 30,
  }
  searchParam.value = {}
  if (item) {
    //从已发货拆单过来的
    form.value.order_status = 40
    searchParam.value = {...item}
    searchPlatformOrder()
  }
}

defineExpose({initFooter, createOrder});

function deleteGoods(idx) {
  form.value.items.splice(idx, 1)
}

const platformGoodsRef = ref()

function addPlatformGoods() {
  const skuIds = form.value.items.filter(it => it.source === 1 || it.sku_id).map(it => it.sku_id)
  const selfProductSkuIds = form.value.items.filter(it => it.source === 2 || it.product_sku_id).map(it => it.product_sku_id)
  const factoryProductSkuIds = form.value.items.filter(it => it.source === 3 || it.product_sku_id).map(it => it.product_sku_id)
  platformGoodsRef.value.init({skuIds, selfProductSkuIds,factoryProductSkuIds}, true, 1, false, false)
}

const canOperate = ref(true)
const msg = useMessage()

async function searchPlatformOrder() {
  if (!searchParam.value.shopId) {
    msg.error('请选择店铺')
    return
  }
  if (!searchParam.value.tid) {
    msg.error('请输入订单号')
    return
  }
  const res = await getOrderByTid({shop_id: searchParam.value.shopId, tid: searchParam.value.tid})
  let {
    order_cipher_info,
    receiver_city,
    receiver_district,
    receiver_province,
    receiver_tel,
    receiver_town,
    items,
    seller_memo,
    type,
    buyer_message
  } = res.data
  const {receiver_address_mask,receiver_name_mask,receiver_phone_mask} = order_cipher_info
  if (seller_memo) {
    seller_memo = JSON.parse(seller_memo).join(',')
  }
  form.value = {
    ...form.value,
    is_platform_order: 1,
    free_order_source: type,
    free_order_no: searchParam.value.tid,
    receiver_name:receiver_name_mask,
    receiver_phone:receiver_phone_mask,
    receiver_tel,
    fullAddress: [receiver_province, receiver_city, receiver_district],
    receiver_town,
    receiver_address:receiver_address_mask,
    seller_memo: seller_memo || '',
    buyer_message: buyer_message || '',
    items: items.map(it => {
      return {
        goods_title: it.goods_title,
        num_iid: it.num_iid,
        sku_num: it.sku_num,
        sku_price: it.sku_price,
        sku_value: it.sku_value,
        sku_id: it.sku_id,
        outer_sku_iid: it.outer_sku_iid
      }
    })
  }
  goodsType.value = 2
  canOperate.value = false
}

function clearTid(){
  searchParam.value.tid = null
  canOperate.value = true
}

function changeGoods(list) {
  if (list?.length) {
    const platformGoods = list.filter(it => it.source === 1)
    if (platformGoods?.length) {
      form.value.items.push(...platformGoods.map(it => {
        const {num_iid, goods_title, sku_value, weight, sku_id, outer_id} = it
        return {
          num_iid, goods_title, sku_value, outer_sku_iid: outer_id, weight, sku_id
        }
      }))
    }
    const selfProducts = list.filter(it => it.source === 2)
    if (selfProducts?.length) {
      form.value.items.push(...selfProducts.map(it => {
        const {product_sku_id, product_id, sku_name, weight, merchant_code, name} = it
        return {
          product_sku_id, product_id, sku_value: sku_name, outer_sku_iid: merchant_code, weight, goods_title: name
        }
      }))
    }
    const factoryProducts = list.filter(it => it.source === 3)
    if (factoryProducts?.length) {
      form.value.items.push(...factoryProducts.map(it => {
        const {product_sku_id, product_id, sku_name, weight, merchant_code, name} = it
        return {
          product_sku_id, product_id, sku_value: sku_name, outer_sku_iid: merchant_code, weight, goods_title: name
        }
      }))
    }
  }
}

const router = useRouter()
const addressSelectRef = ref()

function judgeData() {
  let flag = true
  const {receiver_name, fullAddress, receiver_phone, receiver_tel, receiver_address,items} = form.value
  if (!receiver_name) {
    msg.error("收件人姓名不能为空")
    flag = false;
    return flag;
  }
  if (!receiver_phone && !receiver_tel) {
    msg.error("收件人手机盒收件人电话不能同时为空")
    flag = false;
    return flag;
  }
  if (!fullAddress?.length) {
    msg.error("收件人省市区不能为空")
    flag = false;
    return flag;
  }
  if (!receiver_address) {
    msg.error("收件人详细地址不能为空")
    flag = false;
    return flag;
  }
  if (!items?.length) {
    msg.error("商品信息不能为空")
    flag = false;
    return flag;
  }
  return flag;
}

async function createOrder(continueCreate) {
  if (!judgeData()) {
    return false;
  }
  let params = {...form.value}
  const [receiver_province,receiver_city,receiver_district] = params.fullAddress
  params = {...params,receiver_province,receiver_city,receiver_district}
  params.free_order_source = canOperate.value ? 0 : 1
  const res = await createFreeOrder(params)
  if (res.code !== 200) {
    return false
  }
  msg.success('创建成功')
  if (continueCreate) {
    form.value = {
      fullAddress: [], items: [],
      free_order_source: 0,
      is_platform_order: 0,
      order_status: 30,
    }
    addressSelectRef.value.clearVal()
  }
  setTimeout(()=>{
    router.push('/order/send')
  }, 1000)
  return true
}

</script>

<template>
  <div class="order-free" :style="showFooter ? 'padding: 1.75rem 0.75rem;' : ''">
    <div class="order-free-content">
      <div class="flex_center">
        <el-radio-group v-model="receiverType" text-color="#626aef" fill="rgb(239, 240, 253)">
          <el-radio-button :label="1">关联平台订单</el-radio-button>
        </el-radio-group>
        <shop-select style="width: 200px" v-model="searchParam.shopId"/>
        <el-input style="width: 400px" class="ml-2" v-model="searchParam.tid" placeholder="请输入店铺的订单号查询"/>
        <el-button type="primary" class="ml-2" @click="searchPlatformOrder">查询</el-button>
        <el-button @click="clearTid">清空</el-button>
        <div class="ml-auto">
          <el-button type="primary">导入订单</el-button>
        </div>
      </div>
      <el-card class="mt-5">
        <template #header>
          <div class="flex_center">
            <span class="label-content">收件信息</span>
            <el-input v-model="formdataReveiver.searchKeyword" placeholder="搜索查询收件人信息，填写完整的姓名或手机"/>
            <el-button class="ml-2">管理</el-button>
          </div>
        </template>
        <div class="flex_top">
          <div class="order-content" style="width:65%">
            <div class="flex_center">
              <span class="label-content">收件人姓名：</span>
              <el-input v-model="form.receiver_name" :disabled="!canOperate"/>
              <span class="ml-2 label-content">收件人手机：</span>
              <el-input v-model="form.receiver_phone" :disabled="!canOperate"/>
              <span class="ml-2 label-content">收件人电话：</span>
              <el-input placeholder="选填" v-model="form.receiver_tel" :disabled="!canOperate"/>
              <el-checkbox class="ml-5">保存为常用收件人</el-checkbox>
            </div>
            <div class="mt-3 flex_center">
              <span class="label-content">省/市/区：</span>
              <address-select v-model="form.fullAddress" :disable="!canOperate" class="input_width"
                              ref="addressSelectRef" text="请选择"/>
              <span class="label-content ml-2">详细地址：</span>
              <el-input :disabled="!canOperate" v-model="form.receiver_address"/>
            </div>
            <div class="mt-3 flex_center">
              <span class="label-content">买家留言：</span>
              <el-input v-model="form.buyer_message"/>
            </div>
          </div>
          <div class="ml-auto" style="width: 30%;">
            <el-input type="textarea" :rows="3" placeholder="粘贴收件人信息，自动解析"/>
            <div class="tr">
              <el-button class="mt-3">解析</el-button>
            </div>
          </div>
        </div>
      </el-card>
      <el-card class="mt-5">
        <template #header>
          <div class="flex_center">
            <span>商品信息</span>
            <!--            <el-radio-group v-model="goodsType" class="ml-5">-->
            <!--              <el-radio-button :label="1">简约模式</el-radio-button>-->
            <!--              <el-radio-button :label="2">详细模式</el-radio-button>-->
            <!--            </el-radio-group>-->
          </div>
        </template>
        <div>
          <!--          <div class="order-content" v-if="goodsType === 1">-->
          <!--            <div class="flex_center">-->
          <!--              <span class="label-content">商品类型：</span>-->
          <!--              <el-input class="input_width" v-model="form.goodsType"/>-->
          <!--              <span class="ml-2 label-content">商品重量：</span>-->
          <!--              <el-input class="input_width" v-model="form.weight">-->
          <!--                <template #append>kg</template>-->
          <!--              </el-input>-->
          <!--              <span class="ml-2 label-content">商品体积：</span>-->
          <!--              <el-input class="input_width" v-model="form.volume">-->
          <!--                <template #append>m³</template>-->
          <!--              </el-input>-->
          <!--            </div>-->
          <!--            <div class="mt-3 flex_top">-->
          <!--              <span class="label-content">商品备注：</span>-->
          <!--              <el-input type="textarea" :rows="3" show-word-limit :maxlength="200"/>-->
          <!--            </div>-->
          <!--          </div>-->
          <div class="order-content">
            <el-button type="primary" plain @click="addPlatformGoods">添加商品</el-button>
            <el-table class="mt-3" :data="form.items">
              <el-table-column label="商品标题">
                <template #default="scope">
                  <p>{{ scope.row.goods_title }}</p>
                  <!--                  <el-input v-model="scope.row.goods_title"/>-->
                </template>
              </el-table-column>
              <el-table-column label="规格">
                <template #default="scope">
                  <p>{{ scope.row.sku_value }}</p>
                  <!--                  <el-input v-model="scope.row.sku_value"/>-->
                </template>
              </el-table-column>
              <el-table-column label="数量">
                <template #default="scope">
                  <el-input v-model="scope.row.sku_num"/>
                </template>
              </el-table-column>
              <el-table-column label="金额">
                <template #default="scope">
                  <el-input v-model="scope.row.sku_price"/>
                </template>
              </el-table-column>
              <el-table-column label="编码">
                <template #default="scope">
                  <el-input v-model="scope.row.outer_sku_iid"/>
                </template>
              </el-table-column>
              <el-table-column label="重量">
                <template #default="scope">
                  <el-input v-model="scope.row.weight"/>
                </template>
              </el-table-column>
              <!--              <el-table-column label="体积">-->
              <!--                <template #default="scope">-->
              <!--                  <el-input v-model="scope.row.volume"/>-->
              <!--                </template>-->
              <!--              </el-table-column>-->
              <el-table-column label="操作" width="80">
                <template #default="scope">
                  <a class="red" @click="deleteGoods(scope.$index)">删除</a>
                </template>
              </el-table-column>
            </el-table>
            <!--            <el-button style="width: 100%;border-style: dashed" @click="addGoods">添加商品信息</el-button>-->
          </div>
        </div>
      </el-card>
      <el-card class="mt-5" header="基本信息">
        <div class="flex_center order-content">
          <span class="label-content">订单来源：</span>
          <platform-source class="input_width" v-model="form.free_order_source" :disabled="!canOperate"/>
          <span class="ml-2 label-content">订单编号：</span>
          <el-input class="input_width" v-model="form.free_order_no" :disabled="!canOperate"/>
          <span class="ml-2 label-content">卖家备注：</span>
          <el-input v-model="form.seller_memo"/>
        </div>
      </el-card>
    </div>
    <div class="order-footer flex_center" v-if="showFooter"
         :style="{'width': getMenuCollapseState() ? 'calc(100% - var(--mine-g-sub-aside-collapse-width) - 25px)' : 'calc(100% - var(--mine-g-sub-aside-width) - 25px)'}">
      <el-button type="primary" @click="createOrder(true)">保存并继续创建</el-button>
      <el-button @click="createOrder(false)">保存</el-button>
    </div>
<!--    <select-platform-goods ref="platformGoodsRef" @change="changeGoods"/>-->
    <select-goods ref="platformGoodsRef" @change="changeGoods"/>
  </div>
</template>

<style lang="scss" scoped>
.order-free {
  background: white;

  .order-free-content {
    margin-bottom: 80px;
  }

  .order-content {
    font-size: 14px;
    font-weight: normal;
  }

  .input_width {
    max-width: 200px;
    min-width: 200px;
  }

  .label-content {
    min-width: 85px;
    max-width: 85px;
  }
}

.order-footer {
  justify-content: center;
  height: 60px;
  position: fixed;
  bottom: 0;
  background: white;
  z-index: 111;
  right: 0;
  padding: 0 1rem;
  column-gap: 0.25rem;
}
</style>
