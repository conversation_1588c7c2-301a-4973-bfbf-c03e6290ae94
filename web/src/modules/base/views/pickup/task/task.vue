<script setup>
import NotPickup from './components/NotPickup.vue'
import PreparePurchased from "~/base/views/pickup/task/components/preparePurchased.vue";


const activeMode = ref(0)
const notPickupRef = ref()
const preparePurchasedRef = ref()

function changeMode(e) {
  nextTick(() => {
    switch (e) {
      case 0:
        preparePurchasedRef.value.init()
        break;
      case 1:
        break;
      case 2:
        notPickupRef.value.init()
        break;
      case 3:
        break;
    }
  })
}

onMounted(()=>{
  changeMode(0)
})
</script>

<template>
  <div>
    <div class="purchase-task">
      <el-tabs v-model="activeMode" @tab-change="changeMode" class="tab-select">
        <el-tab-pane label="待采购" :name="0"/>
        <el-tab-pane label="待支付" :name="1"/>
        <el-tab-pane label="待发货" :name="2" />
        <el-tab-pane label="已发货" :name="3"/>
        <!--        <el-tab-pane label="拿货异常" :name="4"/>-->
        <!--        <el-tab-pane label="已拿货统计" :name="5"/>-->
      </el-tabs>
      <div class="opt-button">
      </div>
    </div>
    <PreparePurchased v-if="activeMode===0" ref="preparePurchasedRef"/>
    <NotPickup v-if="activeMode===2" ref="notPickupRef"/>
  </div>

</template>

<style lang="scss">
.hidden-expand-icon .el-table__expand-icon {
  display: none;
}

.el-drawer__header {
  margin-bottom: 0 !important;
}

.hidden-expand-icon {
  width: 0;
  padding: 0;
}

.purchase-task {
  margin: 0.75rem;
  .tab-select {
    background: white;
    padding: 0.75rem;
  }

  .opt-button {
    position: absolute;
    right: 1rem;
    top: 0.75rem;
  }
}
</style>
