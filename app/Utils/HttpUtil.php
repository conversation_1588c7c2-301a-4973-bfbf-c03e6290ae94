<?php

namespace App\Utils;


class HttpUtil
{
    public static function buildUrl(string $url, array $additionalParams = []): string
    {
        Log::info("buildUrl: $url",$additionalParams);
        // 解析原始 URL，获取其组成部分
        $urlParts = parse_url($url);

        // 如果原始 URL 中有查询字符串，将其解析为数组


        $existingParams = isset($urlParts['query']) ? self::parseUrlQueryString($urlParts['query']) : array();
        Log::info("buildUrl: $url",$existingParams);
        // 将新参数与现有参数合并
        $mergedParams = array_merge($existingParams, $additionalParams);
        Log::info("buildUrl: $url",$mergedParams);

        // 重建查询字符串
        $queryString = http_build_query($mergedParams);

        // 更新原始 URL 的查询部分
        $urlParts['query'] = $queryString;

        // 重新组合 URL
        // 重新组合URL
        $newUrl = '';
        if (isset($urlParts['scheme'])) {
            $newUrl .= $urlParts['scheme'].'://';
        }
        if (isset($urlParts['host'])) {
            $newUrl .= $urlParts['host'];
        }
        if (isset($urlParts['path'])) {
            $newUrl .= $urlParts['path'];
        }
        if (!empty($urlParts['query'])) {
            $newUrl .= '?'.$urlParts['query'];
        }
        if (isset($urlParts['fragment'])) {
            $newUrl .= '#'.$urlParts['fragment'];
        }

        return $newUrl;
    }

    /**
     * 解析 URL 查询字符串,先用&分隔，再用= 分割参数和值, 返回一个数组, 其中键为参数名，值为参数值。
     * @param  string  $queryString
     * @return array
     */
    public static function parseUrlQueryString(string $queryString): array
    {

        $params = explode('&', $queryString);
        return array_reduce($params, function ($carry, $item) {
            $parts = explode('=', $item, 2);
            $carry[$parts[0]] = isset($parts[1]) ? urldecode($parts[1]) : null;
            return $carry;
        }, []);

//        return array_map(function ($item) {
//            return urldecode($item);
//        }, explode('&', $queryString));

    }

    /**
     * 拼接路径
     * @param  string  $path1
     * @param  string  $path2
     * @return string
     */

    public static function concatPath(string $path1, string $path2): string
    {
        $path1 = rtrim($path1, '/');
        $path2 = ltrim($path2, '/');
        return $path1 . '/' . $path2;
    }


}
