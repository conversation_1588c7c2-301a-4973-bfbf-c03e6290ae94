<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2022/8/10
 * Time: 22:24
 */

namespace App\Crontab;

use App\Components\Facade4Api\Items\FaItem;
use App\Components\Facade4Api\ServiceFactory;
use App\Components\Facade4Api\Items\FaItemSku;
use App\Repository\Shop\ShopRepository;
use App\Repository\Goods\GoodsRepository;
use App\Repository\Goods\GoodsSkuRepository;
use App\Utils\Log;
use Hyperf\Command\Annotation\Command;
use Hyperf\Command\Command as HyperfCommand;
use Hyperf\Carbon\Carbon;
use Psr\Container\ContainerInterface;

//#[Crontab(rule: "0 * * * * *", singleton: true, onOneServer: true,callback: "handle", memo: "平台同步商品")]
#[Command]
class SyncGoodsCrontab extends HyperfCommand
{

    public function __construct(
        protected ContainerInterface $container,
        protected ShopRepository $shopRepository,
        protected GoodsRepository $goodsRepository,
        protected GoodsSkuRepository $goodsSkuRepository
    ){
        parent::__construct('sync:goods');
    }


    public function handle()
    {
        $this->info('开始同步商品数据...');

        try {
            // 查询所有有效的店铺
            $shops = $this->shopRepository->getQueryByAuthAvailable()->get();

            if ($shops->isEmpty()) {
                $this->info('没有找到有效的店铺');
                return;
            }

            $this->info("找到 {$shops->count()} 个有效店铺");

            foreach ($shops as $shop) {
                $this->syncShopGoods($shop);
            }

            $this->info('商品同步完成');
        } catch (\Exception $e) {
            $this->error('商品同步失败: ' . $e->getMessage());
            Log::error('商品同步失败', ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
        }
    }

    /**
     * 同步单个店铺的商品数据
     */
    protected function syncShopGoods($shop)
    {
        $this->info("开始同步店铺: {$shop->shop_name} (ID: {$shop->id})");

        try {
            $itemsServiceFacade = ServiceFactory::createItemServiceFacade($shop->getPlatform());

            $page = 1;
            $pageSize = 50; // 每页50个商品
            $hasNext = true;
            $totalSynced = 0;

            while ($hasNext) {
                $this->info("正在同步第 {$page} 页商品...");

                $pageData = $itemsServiceFacade->sendGetItems(
                    $shop->access_token,
                    $pageSize,
                    $page,
                    1 // 只同步在售商品
                );

                if (empty($pageData->data)) {
                    $this->info("第 {$page} 页没有商品数据");
                    break;
                }

                // 保存商品数据
                $syncedCount = $this->saveGoodsData($shop, $pageData->data);
                $totalSynced += $syncedCount;

                $this->info("第 {$page} 页同步了 {$syncedCount} 个商品");

                // 检查是否还有下一页
                $hasNext = $pageData->hasNext ?? false;
                $page++;

                // 避免无限循环，最多同步100页
                if ($page > 100) {
                    $this->warn("已达到最大页数限制(100页)，停止同步");
                    break;
                }
            }

            // 更新店铺最后同步时间
            $this->shopRepository->updateById($shop->id, [
                'last_goods_sync_at' => Carbon::now()->toDateTimeString()
            ]);

            $this->info("店铺 {$shop->shop_name} 同步完成，共同步 {$totalSynced} 个商品");

        } catch (\Exception $e) {
            $this->error("店铺 {$shop->shop_name} 同步失败: " . $e->getMessage());
            Log::error('店铺商品同步失败', [
                'shop_id' => $shop->id,
                'shop_name' => $shop->shop_name,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 保存商品数据到数据库
     */
    protected function saveGoodsData($shop, array $goodsDataList): int
    {
        $syncedCount = 0;

        foreach ($goodsDataList as $faItem) {
            /** @var FaItem $faItem */
            try {
                // 转换FaItem对象为商品数据
                $goodsData = [
                    'user_id' => $shop->user_id,
                    'shop_id' => $shop->id,
                    'type' => $shop->type,
                    'num_iid' => $faItem->numIid ?? '',
                    'outer_goods_id' => $faItem->outerId ?: '',
                    'goods_title' => $faItem->title ?? '',
                    'goods_pic' => $faItem->picUrl ?: '',
                    'is_onsale' => $faItem->status ?? 1,
                    'goods_created_at' => $faItem->ptCreatedAt ?? null,
                    'goods_updated_at' => $faItem->ptUpdatedAt ?? null,
                    'created_by' => 0,
                    'updated_by' => 0,
                ];

                // 使用updateOrCreate避免重复插入
                $goods = $this->goodsRepository->updateOrCreate(
                    [
                        'shop_id' => $shop->id,
                        'num_iid' => $goodsData['num_iid']
                    ],
                    $goodsData
                );

                // 保存SKU数据
                if (isset($faItem->itemSkus)) {
                    // FaItem中的itemSkus是Collection对象
                    $this->saveGoodsSkus($shop, $goods, $faItem->itemSkus);
                }

                $syncedCount++;

            } catch (\Exception $e) {
                Log::error('保存商品数据失败', [
                    'shop_id' => $shop->id,
                    'goods_data' => $faItem,
                    'error' => $e->getMessage()
                ]);
                continue;
            }
        }

        return $syncedCount;
    }

    /**
     * 保存商品SKU数据
     */
    protected function saveGoodsSkus($shop, $goods, $skusData)
    {
        // 如果是Collection对象，遍历它
        foreach ($skusData as $faSkuData) {
            try {
                /** @var FaItemSku $faSkuData */
                $skuData = [
                    'goods_id' => $goods->id,
                    'user_id' => $shop->user_id,
                    'shop_id' => $shop->id,
                    'type' => $shop->type,
                    'sku_id' => $faSkuData->skuId ?? '',
                    'sku_value' => $faSkuData->skuName ?? '',
                    'outer_id' => $faSkuData->outId ?: '',
                    'outer_goods_id' => $faSkuData->outItemId ?: '',
                    'sku_pic' => $faSkuData->picUrl ?? $goods->goods_pic, // 如果SKU没有图片，则使用商品主图
                    'is_onsale' => $faSkuData->status ?? 1,
                    'created_by' => 0,
                    'updated_by' => 0,
                ];

                // 使用updateOrCreate避免重复插入
                $this->goodsSkuRepository->updateOrCreate(
                    [
                        'shop_id' => $shop->id,
                        'goods_id' => $goods->id,
                        'sku_id' => $skuData['sku_id']
                    ],
                    $skuData
                );

            } catch (\Exception $e) {
                Log::error('保存SKU数据失败', [
                    'shop_id' => $shop->id,
                    'goods_id' => $goods->id,
                    'sku_data' => $faSkuData,
                    'error' => $e->getMessage()
                ]);
                continue;
            }
        }
    }
}